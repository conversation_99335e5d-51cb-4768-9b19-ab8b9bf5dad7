import Stripe from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is required');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-05-28.basil',
});

export { stripe };

/**
 * Creates a Stripe Checkout Session with embedded UI mode and inline pricing
 */
export async function createEmbeddedCheckoutSession(params: {
  amount: number;
  currency: string;
  quoteId: string;
  workshopName: string;
  returnUrl: string;
}) {
  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    line_items: [
      {
        price_data: {
          currency: params.currency.toLowerCase(),
          product_data: {
            name: params.workshopName,
          },
          unit_amount: Math.round(params.amount * 100), // Convert to cents
        },
        quantity: 1,
      },
    ],
    mode: 'payment',
    ui_mode: 'embedded',
    return_url: params.returnUrl,
    metadata: {
      quote_id: params.quoteId,
    },
  });

  return session;
}

/**
 * Retrieves a Stripe Checkout Session by ID
 */
export async function retrieveCheckoutSession(sessionId: string) {
  return await stripe.checkout.sessions.retrieve(sessionId);
}
