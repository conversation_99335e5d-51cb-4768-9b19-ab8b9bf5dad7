"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Title,
} from "@/components/ui/dialog";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  Sheet<PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";
import { useIsMobile } from "@/hooks/use-mobile";
import type { QuoteWithWorkshopDetails } from "@/types/quote";

interface PaymentDialogProps {
  quote: QuoteWithWorkshopDetails;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

/**
 * Payment dialog component - currently a stub implementation.
 * 
 * TODO: Implement payment provider integration
 * - Add payment provider SDK imports
 * - Implement payment form with card input
 * - Add payment intent creation and confirmation
 * - Handle payment success/failure states
 * - Add proper error handling and loading states
 */
function PaymentForm({ quote }: { quote: QuoteWithWorkshopDetails; onClose: () => void }) {
  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-semibold">Payment Integration Required</h3>
        <p className="text-sm text-muted-foreground mt-2">
          This is a placeholder payment dialog. A payment provider needs to be integrated.
        </p>
      </div>
      
      <div className="border rounded-lg p-4 bg-muted/50">
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>Workshop:</span>
            <span className="font-medium">{quote.workshops?.name}</span>
          </div>
          <div className="flex justify-between">
            <span>Amount:</span>
            <span className="font-medium">
              {quote.currency.toUpperCase()} {quote.price.toFixed(2)}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Status:</span>
            <span className="font-medium capitalize">{quote.status}</span>
          </div>
        </div>
      </div>
      
      <div className="text-xs text-muted-foreground space-y-1">
        <p>To implement payment processing:</p>
        <ul className="list-disc list-inside space-y-1 ml-2">
          <li>Choose a payment provider (Stripe, Square, etc.)</li>
          <li>Follow the integration guide in docs/payment-system-integration.md</li>
          <li>Replace this stub with actual payment form</li>
        </ul>
      </div>
    </div>
  );
}

export function PaymentDialog({
  quote,
  open,
  onOpenChange,
}: PaymentDialogProps) {
  const isMobile = useIsMobile();

  const handleClose = () => {
    onOpenChange(false);
  };

  const content = <PaymentForm quote={quote} onClose={handleClose} />;

  if (isMobile) {
    return (
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent side="bottom" className="max-h-[90vh]">
          <SheetHeader className="text-left">
            <SheetTitle>Payment Required</SheetTitle>
          </SheetHeader>
          <div className="mt-6">{content}</div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Payment Required</DialogTitle>
        </DialogHeader>
        {content}
      </DialogContent>
    </Dialog>
  );
}
