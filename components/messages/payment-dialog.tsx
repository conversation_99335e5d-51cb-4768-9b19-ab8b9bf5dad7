"use client";

import {
  EmbeddedCheckout,
  EmbeddedCheckoutProvider,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { useEffect, useState } from "react";

import { createPaymentIntent } from "@/app/enquiries/payment-actions";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>etH<PERSON><PERSON>,
  She<PERSON>Title,
} from "@/components/ui/sheet";
import { useIsMobile } from "@/hooks/use-mobile";
import type { QuoteWithWorkshopDetails } from "@/types/quote";

const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
);

interface PaymentDialogProps {
  quote: QuoteWithWorkshopDetails;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

/**
 * Payment form component using Stripe Embedded Checkout
 */
function PaymentForm({ quote }: { quote: QuoteWithWorkshopDetails }) {
  const [clientSecret, setClientSecret] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    async function fetchClientSecret() {
      try {
        setLoading(true);
        setError("");

        const result = await createPaymentIntent(quote.id);

        if (!result.success) {
          setError(result.error || "Failed to create payment session");
          return;
        }

        if (result.clientSecret) {
          setClientSecret(result.clientSecret);
        }
      } catch (err) {
        console.error("Error creating payment intent:", err);
        setError("Failed to initialize payment");
      } finally {
        setLoading(false);
      }
    }

    // Allow payment for both pending and payment_intent_created statuses
    if (quote.status === "pending" || quote.status === "payment_intent_created") {
      fetchClientSecret();
    } else {
      setLoading(false);
      setError(`Quote cannot be paid because it is ${quote.status}`);
    }
  }, [quote.id, quote.status]);

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-lg border bg-muted/50 p-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Workshop:</span>
              <span className="font-medium">{quote.workshops?.name}</span>
            </div>
            <div className="flex justify-between">
              <span>Amount:</span>
              <span className="font-medium">
                {quote.currency.toUpperCase()} {quote.price.toFixed(2)}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center space-x-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            <span className="text-sm text-muted-foreground">
              Initializing payment...
            </span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="rounded-lg border bg-muted/50 p-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Workshop:</span>
              <span className="font-medium">{quote.workshops?.name}</span>
            </div>
            <div className="flex justify-between">
              <span>Amount:</span>
              <span className="font-medium">
                {quote.currency.toUpperCase()} {quote.price.toFixed(2)}
              </span>
            </div>
          </div>
        </div>
        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
          <div className="flex items-center space-x-2">
            <svg
              className="h-5 w-5 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span className="text-sm text-red-800">{error}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-lg border bg-muted/50 p-4">
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>Workshop:</span>
            <span className="font-medium">{quote.workshops?.name}</span>
          </div>
          <div className="flex justify-between">
            <span>Amount:</span>
            <span className="font-medium">
              {quote.currency.toUpperCase()} {quote.price.toFixed(2)}
            </span>
          </div>
        </div>
      </div>

      {clientSecret && (
        <div className="rounded-lg border">
          <EmbeddedCheckoutProvider
            stripe={stripePromise}
            options={{ clientSecret }}
          >
            <EmbeddedCheckout />
          </EmbeddedCheckoutProvider>
        </div>
      )}
    </div>
  );
}

export function PaymentDialog({
  quote,
  open,
  onOpenChange,
}: PaymentDialogProps) {
  const isMobile = useIsMobile();
  const content = <PaymentForm quote={quote} />;

  if (isMobile) {
    return (
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent side="bottom" className="max-h-[90vh] overflow-y-auto">
          <SheetHeader className="text-left">
            <SheetTitle>Complete Payment</SheetTitle>
          </SheetHeader>
          <div className="mt-6">{content}</div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Complete Payment</DialogTitle>
        </DialogHeader>
        <div className="mt-4">{content}</div>
      </DialogContent>
    </Dialog>
  );
}
