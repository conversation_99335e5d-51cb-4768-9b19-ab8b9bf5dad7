"use client";

import { useEffect, useState } from "react";

import type { Column } from "@/components/dashboard/responsive-data-display";
import { ResponsiveDataDisplay } from "@/components/dashboard/responsive-data-display";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/format";
import type {
  LedgerEntry,
  ProviderBalance,
} from "@/app/(loggedin)/transactions/actions";
import {
  getProviderBalance,
  getProviderLedgerEntries,
} from "@/app/(loggedin)/transactions/actions";

interface TransactionsViewProps {
  providerId: string;
}

export function TransactionsView({ providerId }: TransactionsViewProps) {
  const [ledgerEntries, setLedgerEntries] = useState<LedgerEntry[]>([]);
  const [balance, setBalance] = useState<ProviderBalance | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        const [ledgerResult, balanceResult] = await Promise.all([
          getProviderLedgerEntries(providerId),
          getProviderBalance(providerId),
        ]);

        if (!ledgerResult.success) {
          setError(ledgerResult.error || "Failed to fetch transactions");
          return;
        }

        if (!balanceResult.success) {
          setError(balanceResult.error || "Failed to fetch balance");
          return;
        }

        setLedgerEntries(ledgerResult.data || []);
        setBalance(balanceResult.data || null);
      } catch (err) {
        setError("An unexpected error occurred");
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [providerId]);

  const formatTransactionType = (type: string) => {
    switch (type) {
      case "payment":
        return "Payment";
      case "withdrawal":
        return "Withdrawal";
      case "fee":
        return "Fee";
      case "refund":
        return "Refund";
      default:
        return type;
    }
  };

  const formatAmount = (amount: number, type: string, currency: string) => {
    const formattedAmount = formatCurrency(Math.abs(amount), currency);
    
    if (type === "payment") {
      return `+${formattedAmount}`;
    } else if (type === "withdrawal" || type === "fee") {
      return `-${formattedAmount}`;
    }
    return formattedAmount;
  };

  const EmptyState = () => (
    <p className="py-8 text-center text-muted-foreground">
      No transactions found
    </p>
  );

  const columns: Column<LedgerEntry>[] = [
    {
      header: "Date",
      accessor: (entry: LedgerEntry) =>
        new Date(entry.created_at).toLocaleDateString(),
    },
    {
      header: "Type",
      accessor: (entry: LedgerEntry) => formatTransactionType(entry.transaction_type),
    },
    {
      header: "Description",
      accessor: (entry: LedgerEntry) => entry.description || "-",
    },
    {
      header: "Amount",
      accessor: (entry: LedgerEntry) => (
        <span
          className={
            entry.transaction_type === "payment"
              ? "text-green-600"
              : entry.transaction_type === "withdrawal" || entry.transaction_type === "fee"
              ? "text-red-600"
              : ""
          }
        >
          {formatAmount(entry.amount, entry.transaction_type, entry.currency)}
        </span>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Transactions</h1>
        <div className="animate-pulse">
          <Card>
            <CardHeader>
              <CardTitle>Loading...</CardTitle>
            </CardHeader>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Transactions</h1>
        <Card>
          <CardContent className="p-6">
            <p className="text-red-600">Error: {error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Transactions</h1>
      
      {balance && (
        <Card>
          <CardHeader>
            <CardTitle>Current Balance</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">
              {formatCurrency(balance.balance, balance.currency)}
            </p>
          </CardContent>
        </Card>
      )}

      <ResponsiveDataDisplay
        data={ledgerEntries}
        columns={columns}
        emptyState={<EmptyState />}
        cardFields={{
          title: (entry: LedgerEntry) =>
            `${formatTransactionType(entry.transaction_type)} - ${new Date(entry.created_at).toLocaleDateString()}`,
          content: [
            {
              label: "Amount",
              value: (entry: LedgerEntry) => formatAmount(entry.amount, entry.transaction_type, entry.currency),
              layout: "row",
            },
            {
              label: "Description",
              value: (entry: LedgerEntry) => entry.description || "-",
              layout: "row",
            },
          ],
        }}
      />
    </div>
  );
}